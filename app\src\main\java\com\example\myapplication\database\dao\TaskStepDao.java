package com.example.myapplication.database.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.example.myapplication.database.entities.TaskStepEntity;

import java.util.List;

@Dao
public interface TaskStepDao {
    
    @Query("SELECT * FROM task_steps WHERE taskId = :taskId ORDER BY stepOrder ASC")
    LiveData<List<TaskStepEntity>> getStepsByTaskId(int taskId);

    @Query("SELECT * FROM task_steps WHERE taskId = :taskId ORDER BY stepOrder ASC")
    List<TaskStepEntity> getStepsByTaskIdSync(int taskId);

    @Query("SELECT * FROM task_steps WHERE id = :stepId")
    TaskStepEntity getStepById(int stepId);

    @Query("SELECT COUNT(*) FROM task_steps WHERE taskId = :taskId")
    int getStepsCountByTaskId(int taskId);

    @Query("SELECT COUNT(*) FROM task_steps WHERE taskId = :taskId AND completed = 1")
    int getCompletedStepsCountByTaskId(int taskId);

    @Insert
    void insert(TaskStepEntity step);

    @Insert
    long insertSync(TaskStepEntity step);

    @Insert
    void insertAll(List<TaskStepEntity> steps);

    @Update
    void update(TaskStepEntity step);

    @Delete
    void delete(TaskStepEntity step);

    @Query("DELETE FROM task_steps WHERE taskId = :taskId")
    void deleteStepsByTaskId(int taskId);

    @Query("UPDATE task_steps SET stepOrder = :newOrder WHERE id = :stepId")
    void updateStepOrder(int stepId, int newOrder);
}
