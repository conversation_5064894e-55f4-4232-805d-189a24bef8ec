package com.example.myapplication.models;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class Task {
    private String id;
    private String title;
    private String description;
    private Category category;
    private Date dueDate;
    private Date startTime;
    private Date endTime;
    private boolean completed;
    private Priority priority;
    private Date createdAt;
    private Date completedAt;
    private List<TaskStep> steps;

    public enum Priority {
        LOW("Thấp", "#4CAF50"),
        MEDIUM("Trung bình", "#FF9800"),
        HIGH("Cao", "#F44336");

        private final String displayName;
        private final String color;

        Priority(String displayName, String color) {
            this.displayName = displayName;
            this.color = color;
        }

        public String getDisplayName() { return displayName; }
        public String getColor() { return color; }

        @Override
        public String toString() {
            return displayName; // This will be used in Spinner
        }
    }

    // Constructors
    public Task() {
        this.createdAt = new Date();
        this.steps = new ArrayList<>();
    }

    public Task(String id, String title, String description, Category category, Date dueDate, Priority priority) {
        this.id = id;
        this.title = title;
        this.description = description;
        this.category = category;
        this.dueDate = dueDate;
        this.priority = priority;
        this.completed = false;
        this.createdAt = new Date();
    }

    // Getters and Setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }

    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public Category getCategory() { return category; }
    public void setCategory(Category category) { this.category = category; }

    public Date getDueDate() { return dueDate; }
    public void setDueDate(Date dueDate) { this.dueDate = dueDate; }

    public boolean isCompleted() { return completed; }
    public void setCompleted(boolean completed) { 
        this.completed = completed;
        if (completed && completedAt == null) {
            this.completedAt = new Date();
        } else if (!completed) {
            this.completedAt = null;
        }
    }

    public Priority getPriority() { return priority; }
    public void setPriority(Priority priority) { this.priority = priority; }

    public Date getStartTime() { return startTime; }
    public void setStartTime(Date startTime) { this.startTime = startTime; }

    public Date getEndTime() { return endTime; }
    public void setEndTime(Date endTime) { this.endTime = endTime; }

    public Date getCreatedAt() { return createdAt; }
    public void setCreatedAt(Date createdAt) { this.createdAt = createdAt; }

    public Date getCompletedAt() { return completedAt; }
    public void setCompletedAt(Date completedAt) { this.completedAt = completedAt; }

    public List<TaskStep> getSteps() { return steps; }
    public void setSteps(List<TaskStep> steps) { this.steps = steps; }

    // Helper methods for steps
    public void addStep(TaskStep step) {
        if (steps == null) {
            steps = new ArrayList<>();
        }
        steps.add(step);
    }

    public void removeStep(TaskStep step) {
        if (steps != null) {
            steps.remove(step);
        }
    }

    public int getCompletedStepsCount() {
        if (steps == null) return 0;
        int count = 0;
        for (TaskStep step : steps) {
            if (step.isCompleted()) count++;
        }
        return count;
    }

    public int getTotalStepsCount() {
        return steps == null ? 0 : steps.size();
    }

    public float getProgressPercentage() {
        if (getTotalStepsCount() == 0) return 0f;
        return (float) getCompletedStepsCount() / getTotalStepsCount() * 100f;
    }

    // Helper methods
    public boolean isOverdue() {
        // Check if task is overdue based on end time
        return endTime != null && !completed && new Date().after(endTime);
    }

    public String getStatusText() {
        if (completed) return "Hoàn thành";
        if (isOverdue()) return "Quá hạn";
        return "Đang thực hiện";
    }

    public int getStatusColor() {
        if (completed) return android.graphics.Color.parseColor("#4CAF50");
        if (isOverdue()) return android.graphics.Color.parseColor("#F44336");
        return android.graphics.Color.parseColor("#FF9800");
    }
}
