package com.example.myapplication.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Index;
import androidx.room.PrimaryKey;

import com.example.myapplication.models.User;

import java.util.Date;

@Entity(tableName = "users",
        indices = {
            @Index(value = "username", unique = true),
            @Index(value = "email", unique = true)
        })
public class UserEntity {
    @PrimaryKey(autoGenerate = true)
    private int id;

    @ColumnInfo(name = "username")
    private String username;

    @ColumnInfo(name = "email")
    private String email;

    @ColumnInfo(name = "full_name")
    private String fullName;

    @ColumnInfo(name = "password_hash")
    private String passwordHash;

    @ColumnInfo(name = "avatar_url")
    private String avatarUrl;

    @ColumnInfo(name = "created_at")
    private Date createdAt;

    @ColumnInfo(name = "last_login_at")
    private Date lastLoginAt;

    @ColumnInfo(name = "is_active")
    private boolean isActive;

    // Constructors
    public UserEntity() {}

    public UserEntity(String username, String email, String fullName, String passwordHash) {
        this.username = username;
        this.email = email;
        this.fullName = fullName;
        this.passwordHash = passwordHash;
        this.createdAt = new Date();
        this.isActive = true;
    }

    // Getters and Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getPasswordHash() {
        return passwordHash;
    }

    public void setPasswordHash(String passwordHash) {
        this.passwordHash = passwordHash;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getLastLoginAt() {
        return lastLoginAt;
    }

    public void setLastLoginAt(Date lastLoginAt) {
        this.lastLoginAt = lastLoginAt;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    // Convert to User model
    public User toUser() {
        User user = new User();
        user.setId(String.valueOf(this.id));
        user.setUsername(this.username);
        user.setEmail(this.email);
        user.setFullName(this.fullName);
        user.setAvatarUrl(this.avatarUrl);
        user.setPasswordHash(this.passwordHash);
        user.setCreatedAt(this.createdAt);
        user.setLastLoginAt(this.lastLoginAt);
        return user;
    }

    // Create from User model
    public static UserEntity fromUser(User user) {
        UserEntity entity = new UserEntity();
        if (user.getId() != null) {
            entity.setId(Integer.parseInt(user.getId()));
        }
        entity.setUsername(user.getUsername());
        entity.setEmail(user.getEmail());
        entity.setFullName(user.getFullName());
        entity.setPasswordHash(user.getPasswordHash());
        entity.setAvatarUrl(user.getAvatarUrl());
        entity.setCreatedAt(user.getCreatedAt());
        entity.setLastLoginAt(user.getLastLoginAt());
        entity.setActive(true);
        return entity;
    }
}
