package com.example.myapplication.models;

public class Category {
    private String id;
    private String name;
    private String color;
    private String icon;
    private int taskCount;

    // Constructors
    public Category() {}

    public Category(String id, String name, String color, String icon) {
        this.id = id;
        this.name = name;
        this.color = color;
        this.icon = icon;
        this.taskCount = 0;
    }

    // Getters and Setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getColor() { return color; }
    public void setColor(String color) { this.color = color; }

    public String getIcon() { return icon; }
    public void setIcon(String icon) { this.icon = icon; }

    public int getTaskCount() { return taskCount; }
    public void setTaskCount(int taskCount) { this.taskCount = taskCount; }

    // Helper methods
    public int getColorInt() {
        try {
            return android.graphics.Color.parseColor(color);
        } catch (IllegalArgumentException e) {
            return android.graphics.Color.parseColor("#2196F3"); // Default blue
        }
    }

    @Override
    public String toString() {
        return name; // This will be used in Spinner
    }

    // Predefined categories
    public static Category[] getDefaultCategories() {
        return new Category[] {
            new Category("1", "Cá nhân", "#E91E63", "👤"),
            new Category("2", "Công việc", "#2196F3", "💼"),
            new Category("3", "Học tập", "#4CAF50", "📚"),
            new Category("4", "Sức khỏe", "#FF9800", "🏃"),
            new Category("5", "Mua sắm", "#9C27B0", "🛒"),
            new Category("6", "Gia đình", "#FF5722", "👨‍👩‍👧‍👦")
        };
    }
}
