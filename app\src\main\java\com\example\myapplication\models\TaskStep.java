package com.example.myapplication.models;

import java.util.Date;

public class TaskStep {
    private String id;
    private String title;
    private String description;
    private boolean completed;
    private Date createdAt;
    private Date completedAt;
    private int order; // Thứ tự của step trong task

    // Constructors
    public TaskStep() {
        this.createdAt = new Date();
        this.completed = false;
    }

    public TaskStep(String id, String title, String description) {
        this();
        this.id = id;
        this.title = title;
        this.description = description;
    }

    public TaskStep(String id, String title, String description, int order) {
        this(id, title, description);
        this.order = order;
    }

    // Getters and Setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }

    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public boolean isCompleted() { return completed; }
    public void setCompleted(boolean completed) { 
        this.completed = completed;
        if (completed && completedAt == null) {
            this.completedAt = new Date();
        } else if (!completed) {
            this.completedAt = null;
        }
    }

    public Date getCreatedAt() { return createdAt; }
    public void setCreatedAt(Date createdAt) { this.createdAt = createdAt; }

    public Date getCompletedAt() { return completedAt; }
    public void setCompletedAt(Date completedAt) { this.completedAt = completedAt; }

    public int getOrder() { return order; }
    public void setOrder(int order) { this.order = order; }

    // Helper methods
    public String getStatusText() {
        return completed ? "Hoàn thành" : "Chưa hoàn thành";
    }

    public int getStatusColor() {
        return completed ? 
            android.graphics.Color.parseColor("#4CAF50") : 
            android.graphics.Color.parseColor("#757575");
    }

    @Override
    public String toString() {
        return title;
    }
}
