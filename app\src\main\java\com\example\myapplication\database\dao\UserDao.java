package com.example.myapplication.database.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.example.myapplication.database.entities.UserEntity;

import java.util.Date;
import java.util.List;

@Dao
public interface UserDao {

    @Query("SELECT * FROM users WHERE id = :userId")
    LiveData<UserEntity> getUserById(int userId);

    @Query("SELECT * FROM users WHERE id = :userId")
    UserEntity getUserByIdSync(int userId);

    @Query("SELECT * FROM users WHERE username = :username LIMIT 1")
    UserEntity getUserByUsername(String username);

    @Query("SELECT * FROM users WHERE email = :email LIMIT 1")
    UserEntity getUserByEmail(String email);

    @Query("SELECT * FROM users WHERE username = :username AND password_hash = :passwordHash LIMIT 1")
    UserEntity authenticateUser(String username, String passwordHash);

    @Query("SELECT * FROM users WHERE is_active = 1 ORDER BY created_at DESC")
    LiveData<List<UserEntity>> getAllActiveUsers();

    @Query("SELECT COUNT(*) FROM users WHERE username = :username")
    int checkUsernameExists(String username);

    @Query("SELECT COUNT(*) FROM users WHERE email = :email")
    int checkEmailExists(String email);

    @Insert
    long insertUser(UserEntity user);

    @Update
    void updateUser(UserEntity user);

    @Query("UPDATE users SET last_login_at = :loginTime WHERE id = :userId")
    void updateLastLogin(int userId, Date loginTime);

    @Query("UPDATE users SET avatar_url = :avatarUrl WHERE id = :userId")
    void updateUserAvatar(int userId, String avatarUrl);

    @Query("UPDATE users SET password_hash = :newPasswordHash WHERE id = :userId")
    void updateUserPassword(int userId, String newPasswordHash);

    @Delete
    void deleteUser(UserEntity user);

    @Query("DELETE FROM users WHERE id = :userId")
    void deleteUserById(int userId);

    @Query("UPDATE users SET is_active = 0 WHERE id = :userId")
    void deactivateUser(int userId);

    @Query("UPDATE users SET is_active = 1 WHERE id = :userId")
    void activateUser(int userId);
}
